use crate::{
    Currency, EdgeDirection, ORDER_FILTER_LOT_SIZE_INDEX, ORDER_FILTER_MIN_ORDER_QTY_INDEX,
    ORDER_FILTERS, ORDER_QUANTITIES, PREDEFINED_RINGS, TAKER_FEE_INDEX, TRADING_FEES,
    TRADING_PAIR_RATES, TRADING_PAIR_TO_RING_INDEX, TradingPair,
};

// BNB 折扣率 (75% 折扣，即支付原价的 75%)
const BNB_DISCOUNT: f64 = 0.75;

fn adjust_quantity(qty: f64, min_qty: f64, step: f64) -> Option<f64> {
    if qty < min_qty {
        return None;
    }
    let steps = (qty / step).floor();
    let adjusted = steps * step;
    if adjusted >= min_qty {
        Some(adjusted)
    } else {
        let min_multiple = (min_qty / step).ceil() * step;
        if min_multiple > qty {
            None
        } else {
            Some(min_multiple)
        }
    }
}

fn quote_to_base_quantity(quote: f64, pair: TradingPair) -> f64 {
    unsafe { quote * TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] }
}

fn base_to_quote_quantity(base: f64, pair: TradingPair) -> f64 {
    unsafe { base * TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] }
}

pub struct ArbitrageEngine {}

impl ArbitrageEngine {
    pub fn new() -> Self {
        Self {}
    }

    pub fn update_rate<T: Into<TradingPair>>(pair: T, bid: f64, ask: f64) {
        let pair = pair.into();
        unsafe {
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] = 1.0 / ask;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid;
        }
    }

    pub fn compute_orders(circle_index: usize) -> Option<usize> {
        let ring = PREDEFINED_RINGS[circle_index];
        for try_count in 0..100 {
            let mut init_amount: f64 = 0.0;
            let mut last_asset_q = 0.0f64;
            let mut i = 0;
            while i < ring.len() {
                let (pair, direction) = ring[i];
                let order_filters = ORDER_FILTERS[pair as usize];
                if i == 0 {
                    if try_count == 0 {
                        let init_q = match pair.quote() {
                            Currency::XETH => 0.008f64,
                            Currency::XBTC => 0.00002f64,
                            _ => 20.0f64,
                        };
                        match direction {
                            EdgeDirection::Forward => unsafe {
                                let base_asset_q = quote_to_base_quantity(init_q, pair);
                                let base_asset_q_adjusted = adjust_quantity(
                                    base_asset_q,
                                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                                )?;
                                ORDER_QUANTITIES[i] = base_asset_q_adjusted;
                                init_amount = base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                last_asset_q = ORDER_QUANTITIES[i];
                            },
                            EdgeDirection::Reverse => unsafe {
                                let base_asset_q_adjusted = adjust_quantity(
                                    init_q,
                                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                                )?;
                                last_asset_q = base_to_quote_quantity(base_asset_q_adjusted, pair);
                                ORDER_QUANTITIES[i] = base_asset_q_adjusted;
                                init_amount = base_asset_q_adjusted;
                            },
                        }
                    } else {
                        unsafe {
                            ORDER_QUANTITIES[i] += order_filters[ORDER_FILTER_LOT_SIZE_INDEX];
                            match direction {
                                EdgeDirection::Forward => {
                                    init_amount = base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                    last_asset_q = ORDER_QUANTITIES[i];
                                }
                                EdgeDirection::Reverse => {
                                    init_amount = ORDER_QUANTITIES[i];
                                    last_asset_q =
                                        base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                }
                            }
                        }
                    }
                    i += 1;
                    continue;
                }
                let q = match direction {
                    EdgeDirection::Forward => quote_to_base_quantity(last_asset_q, pair),
                    EdgeDirection::Reverse => last_asset_q,
                };
                let q = match adjust_quantity(
                    q,
                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                ) {
                    Some(r) => r,
                    None => break,
                };
                unsafe {
                    ORDER_QUANTITIES[i] = q;
                }
                last_asset_q = match direction {
                    EdgeDirection::Forward => q,
                    EdgeDirection::Reverse => base_to_quote_quantity(q, pair),
                };
                i += 1;
            }
            if i < ring.len() {
                continue;
            }
            match ring[ring.len() - 1].1 {
                EdgeDirection::Forward => unsafe {
                    if ORDER_QUANTITIES[ring.len() - 1] > init_amount {
                        return Some(ring.len());
                    } else {
                        return None;
                    }
                },
                EdgeDirection::Reverse => {
                    if last_asset_q > init_amount {
                        return Some(ring.len());
                    } else {
                        return None;
                    }
                }
            }
        }
        None
    }

    // #[perf_macro::measure]
    pub fn check_arbitrage<T: Into<TradingPair>>(pair: T) -> Option<usize> {
        let pair = pair.into();
        let ring_indices = TRADING_PAIR_TO_RING_INDEX[pair as usize];
        let mut max_product = 0.0f64;
        let mut result: usize = 0;
        for index in ring_indices {
            let ring = PREDEFINED_RINGS[*index];
            let mut product = 1.0;
            let mut total_fee = 0.0;

            for &(pair, dir) in ring {
                let rate = unsafe { TRADING_PAIR_RATES[pair as usize][dir as usize] };
                if rate == 0.0 {
                    return None;
                }
                product *= rate;

                // 计算该交易对的实际手续费 (taker fee * BNB 折扣)
                let taker_fee = unsafe { TRADING_FEES[pair as usize][TAKER_FEE_INDEX] };
                // let actual_fee = taker_fee * BNB_DISCOUNT;
                total_fee += if taker_fee == 0.0 {
                    0.0
                } else {
                    0.0001725 * 1.1
                };
            }

            // 检查套利机会：产品必须大于 (1 + 总手续费)
            let threshold = 1.0 + total_fee;
            if product > 0.9999 && product > max_product {
                result = *index;
                max_product = product;
            }
        }
        if max_product > 0.0 {
            Some(result)
        } else {
            None
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    // fn find_ring(pair1: TradingPair, pair2: TradingPair, pair3: TradingPair) -> usize {
    //     let mut index = 0;
    //     for i in 0..PREDEFINED_RINGS.len() {
    //         if PREDEFINED_RINGS[i][0].0 == pair1
    //             && PREDEFINED_RINGS[i][1].0 == pair2
    //             && PREDEFINED_RINGS[i][2].0 == pair3
    //         {
    //             index = i;
    //             break;
    //         }
    //     }
    //     println!("index: {}", index);
    //     println!("ring: {:?}", PREDEFINED_RINGS[index]);
    //     index
    // }

    #[test]
    fn test_compute_orders() {
        // ArbitrageEngine::update_rate(TradingPair::XETHBTC, 0.02512, 0.02513);
        // ArbitrageEngine::update_rate(TradingPair::XBTCUSDT, 103919.21, 103919.22);
        // ArbitrageEngine::update_rate(TradingPair::XETHUSDT, 2509.46, 2509.47);
        // let index = find_ring(
        //     TradingPair::XBTCUSDT,
        //     TradingPair::XETHBTC,
        //     TradingPair::XETHUSDT,
        // );
        // let result = ArbitrageEngine::compute_orders(index);
        // assert_eq!(result, None);
        // println!("{:?}", unsafe { ORDER_QUANTITIES });
    }

    #[test]
    fn test_compute_orders2() {
        // ArbitrageEngine::update_rate(TradingPair::XBTCUSDT, 103919.21, 103919.22);
        // ArbitrageEngine::update_rate(TradingPair::XADAUSDT, 0.8053, 0.8054);
        // ArbitrageEngine::update_rate(TradingPair::XADABTC, 0.00000777, 0.00000778);
        // let index = find_ring(
        //     TradingPair::XBTCUSDT,
        //     TradingPair::XADAUSDT,
        //     TradingPair::XADABTC,
        // );
        // let result = ArbitrageEngine::compute_orders(index);
        // assert_eq!(result, Some(3));
        // println!("{:?}", unsafe { ORDER_QUANTITIES });
    }

    #[test]
    fn test_compute_orders3() {
        // ArbitrageEngine::update_rate(TradingPair::XCHZBTC, 0.00000046, 0.00000047);
        // ArbitrageEngine::update_rate(TradingPair::XCHZUSDC, 0.04809, 0.0481);
        // ArbitrageEngine::update_rate(TradingPair::XBTCUSDC, 103919.21, 103919.22);
        // let index = find_ring(
        //     TradingPair::XCHZBTC,
        //     TradingPair::XCHZUSDC,
        //     TradingPair::XBTCUSDC,
        // );
        // let result = ArbitrageEngine::compute_orders(index);
        // assert_eq!(result, Some(3));
        // println!("{:?}", unsafe { ORDER_QUANTITIES });
    }
}
