use crate::{
    Currency, EdgeDirection, ORDER_FILTER_LOT_SIZE_INDEX, ORDER_FILTER_MIN_ORDER_QTY_INDEX,
    ORDER_FILTERS, ORDER_QUANTITIES, PREDEFINED_RINGS, TAKER_FEE_INDEX, TRADING_FEES,
    TRADING_PAIR_RATES, TRADING_PAIR_TO_RING_INDEX, TradingPair,
};

// BNB 折扣率 (75% 折扣，即支付原价的 75%)
const BNB_DISCOUNT: f64 = 0.75;

fn adjust_quantity(qty: f64, min_qty: f64, step: f64) -> Option<f64> {
    if qty < min_qty {
        return None;
    }
    let steps = (qty / step).floor();
    let adjusted = steps * step;
    if adjusted >= min_qty {
        Some(adjusted)
    } else {
        let min_multiple = (min_qty / step).ceil() * step;
        if min_multiple > qty {
            None
        } else {
            Some(min_multiple)
        }
    }
}

fn quote_to_base_quantity(quote: f64, pair: TradingPair) -> f64 {
    unsafe { quote * TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] }
}

fn base_to_quote_quantity(base: f64, pair: TradingPair) -> f64 {
    unsafe { base * TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] }
}

pub struct ArbitrageEngine {}

impl ArbitrageEngine {
    pub fn new() -> Self {
        Self {}
    }

    pub fn update_rate<T: Into<TradingPair>>(pair: T, bid: f64, ask: f64) {
        let pair = pair.into();
        unsafe {
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] = 1.0 / ask;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid;
        }
    }

    pub fn compute_orders(circle_index: usize) -> Option<usize> {
        let ring = PREDEFINED_RINGS[circle_index];
        for try_count in 0..100 {
            let mut init_amount: f64 = 0.0;
            let mut last_asset_q = 0.0f64;
            let mut i = 0;
            while i < ring.len() {
                let (pair, direction) = ring[i];
                let order_filters = ORDER_FILTERS[pair as usize];
                if i == 0 {
                    if try_count == 0 {
                        let init_q = match pair.quote() {
                            Currency::XETH => 0.0001f64,
                            Currency::XBTC => 0.00002f64,
                            _ => 10.0f64,
                        };
                        match direction {
                            EdgeDirection::Forward => unsafe {
                                let base_asset_q = quote_to_base_quantity(init_q, pair);
                                let base_asset_q_adjusted = adjust_quantity(
                                    base_asset_q,
                                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                                )?;
                                ORDER_QUANTITIES[i] = base_asset_q_adjusted;
                                init_amount = base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                last_asset_q = ORDER_QUANTITIES[i];
                            },
                            EdgeDirection::Reverse => unsafe {
                                let base_asset_q_adjusted = adjust_quantity(
                                    init_q,
                                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                                )?;
                                last_asset_q = base_to_quote_quantity(base_asset_q_adjusted, pair);
                                ORDER_QUANTITIES[i] = base_asset_q_adjusted;
                                init_amount = base_asset_q_adjusted;
                            },
                        }
                    } else {
                        unsafe {
                            ORDER_QUANTITIES[i] += order_filters[ORDER_FILTER_LOT_SIZE_INDEX];
                            match direction {
                                EdgeDirection::Forward => {
                                    init_amount = base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                    last_asset_q = ORDER_QUANTITIES[i];
                                }
                                EdgeDirection::Reverse => {
                                    init_amount = ORDER_QUANTITIES[i];
                                    last_asset_q =
                                        base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                }
                            }
                        }
                    }
                    i += 1;
                    continue;
                }
                let q = match direction {
                    EdgeDirection::Forward => quote_to_base_quantity(last_asset_q, pair),
                    EdgeDirection::Reverse => last_asset_q,
                };
                let q = match adjust_quantity(
                    q,
                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                ) {
                    Some(r) => r,
                    None => break,
                };
                unsafe {
                    ORDER_QUANTITIES[i] = q;
                }
                last_asset_q = match direction {
                    EdgeDirection::Forward => q,
                    EdgeDirection::Reverse => base_to_quote_quantity(q, pair),
                };
                i += 1;
            }
            if i < ring.len() {
                continue;
            }
            match ring[ring.len() - 1].1 {
                EdgeDirection::Forward => unsafe {
                    if ORDER_QUANTITIES[ring.len() - 1] > init_amount {
                        return Some(ring.len());
                    } else {
                        return None;
                    }
                },
                EdgeDirection::Reverse => {
                    if last_asset_q > init_amount {
                        return Some(ring.len());
                    } else {
                        return None;
                    }
                }
            }
        }
        None
    }

    // #[perf_macro::measure]
    pub fn check_arbitrage<T: Into<TradingPair>>(pair: T) -> Option<usize> {
        let pair = pair.into();
        let ring_indices = TRADING_PAIR_TO_RING_INDEX[pair as usize];
        let mut max_product = 0.0f64;
        let mut result: usize = 0;
        for index in ring_indices {
            let ring = PREDEFINED_RINGS[*index];
            let mut product = 1.0;
            let mut total_fee = 0.0;

            for &(pair, dir) in ring {
                let rate = unsafe { TRADING_PAIR_RATES[pair as usize][dir as usize] };
                if rate == 0.0 {
                    return None;
                }
                product *= rate;

                // 计算该交易对的实际手续费 (taker fee * BNB 折扣)
                let taker_fee = unsafe { TRADING_FEES[pair as usize][TAKER_FEE_INDEX] };
                // let actual_fee = taker_fee * BNB_DISCOUNT;
                total_fee += if taker_fee == 0.0 {
                    0.0
                } else {
                    0.0001725 * 1.1
                };
            }

            // 检查套利机会：产品必须大于 (1 + 总手续费)
            let threshold = 1.0 + total_fee;
            if product > 0.9999 && product > max_product {
                result = *index;
                max_product = product;
            }
        }
        if max_product > 0.0 {
            Some(result)
        } else {
            None
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn find_ring(pair1: TradingPair, pair2: TradingPair, pair3: TradingPair) -> Option<usize> {
        for i in 0..PREDEFINED_RINGS.len() {
            let ring = PREDEFINED_RINGS[i];
            if ring.len() >= 3 && ring[0].0 == pair1 && ring[1].0 == pair2 && ring[2].0 == pair3 {
                return Some(i);
            }
        }
        None
    }

    fn setup_test_rates() {
        // 清零所有汇率
        unsafe {
            use std::ptr::addr_of_mut;
            let rates_ptr = addr_of_mut!(TRADING_PAIR_RATES);
            for i in 0..28 {
                // 硬编码长度以避免引用静态变量
                (*rates_ptr)[i][EdgeDirection::Forward as usize] = 0.0;
                (*rates_ptr)[i][EdgeDirection::Reverse as usize] = 0.0;
            }
            // 清零订单数量
            let quantities_ptr = addr_of_mut!(ORDER_QUANTITIES);
            for i in 0..10 {
                // 硬编码长度以避免引用静态变量
                (*quantities_ptr)[i] = 0.0;
            }
        }
    }

    #[test]
    fn test_adjust_quantity_basic() {
        // 测试基本的数量调整功能
        assert_eq!(adjust_quantity(10.0, 5.0, 1.0), Some(10.0));
        assert_eq!(adjust_quantity(10.5, 5.0, 1.0), Some(10.0));
        assert_eq!(adjust_quantity(4.0, 5.0, 1.0), None);
        assert_eq!(adjust_quantity(5.0, 5.0, 1.0), Some(5.0));
    }

    #[test]
    fn test_adjust_quantity_step_size() {
        // 测试步长调整 - 使用浮点数比较来避免精度问题
        let result1 = adjust_quantity(10.25, 5.0, 0.1);
        assert!(result1.is_some());
        assert!((result1.unwrap() - 10.2).abs() < 1e-10);

        let result2 = adjust_quantity(10.05, 5.0, 0.1);
        assert!(result2.is_some());
        assert!((result2.unwrap() - 10.0).abs() < 1e-10);

        assert_eq!(adjust_quantity(4.95, 5.0, 0.1), None);
    }

    #[test]
    fn test_adjust_quantity_edge_cases() {
        // 测试边界情况

        // 1. 零值测试
        assert_eq!(adjust_quantity(0.0, 0.0, 1.0), Some(0.0));
        assert_eq!(adjust_quantity(0.0, 1.0, 1.0), None);

        // 2. 步长为零的情况 - 这可能导致除零错误或无限循环
        // 这是一个潜在的 bug！
        // assert_eq!(adjust_quantity(10.0, 5.0, 0.0), None); // 这会导致除零

        // 3. 负数测试
        assert_eq!(adjust_quantity(-10.0, 5.0, 1.0), None);
        assert_eq!(adjust_quantity(10.0, -5.0, 1.0), Some(10.0)); // 负的最小值？

        // 4. 非常小的步长
        let result = adjust_quantity(1.0, 0.5, 1e-10);
        assert!(result.is_some());

        // 5. 数量刚好等于最小值
        assert_eq!(adjust_quantity(5.0, 5.0, 1.0), Some(5.0));

        // 6. 数量略小于最小值但在一个步长内
        assert_eq!(adjust_quantity(4.9, 5.0, 1.0), None);

        // 7. 非常大的数值
        let large_qty = 1e15;
        let result = adjust_quantity(large_qty, 1e10, 1e5);
        assert!(result.is_some());
    }

    #[test]
    fn test_adjust_quantity_min_multiple_logic() {
        // 测试 min_multiple 逻辑中的潜在 bug

        // 当 qty < min_qty 时，函数会计算 min_multiple
        // 这里测试这个逻辑是否正确

        // 情况1: min_multiple 刚好等于 qty
        let result = adjust_quantity(5.0, 5.1, 0.1);
        // min_multiple = ceil(5.1 / 0.1) * 0.1 = 51 * 0.1 = 5.1
        // 5.1 > 5.0，所以应该返回 None
        assert_eq!(result, None);

        // 情况2: min_multiple 小于 qty
        let result = adjust_quantity(5.2, 5.1, 0.1);
        // 这应该返回 Some，因为 5.2 >= 5.1
        assert!(result.is_some());

        // 情况3: 测试 ceil 计算的精度问题
        let result = adjust_quantity(1.0, 1.01, 0.01);
        // min_multiple = ceil(1.01 / 0.01) * 0.01 = 101 * 0.01 = 1.01
        // 1.01 > 1.0，所以应该返回 None
        assert_eq!(result, None);
    }

    #[test]
    fn test_quote_to_base_quantity() {
        setup_test_rates();
        // 设置 ETHUSDT 汇率: ask = 2500, bid = 2499
        ArbitrageEngine::update_rate(TradingPair::XETHUSDT, 2499.0, 2500.0);

        // quote_to_base_quantity 使用 Forward 方向 (1/ask)
        let result = quote_to_base_quantity(2500.0, TradingPair::XETHUSDT);
        assert!((result - 1.0).abs() < 0.0001); // 2500 * (1/2500) = 1.0
    }

    #[test]
    fn test_base_to_quote_quantity() {
        setup_test_rates();
        // 设置 ETHUSDT 汇率: ask = 2500, bid = 2499
        ArbitrageEngine::update_rate(TradingPair::XETHUSDT, 2499.0, 2500.0);

        // base_to_quote_quantity 使用 Reverse 方向 (bid)
        let result = base_to_quote_quantity(1.0, TradingPair::XETHUSDT);
        assert!((result - 2499.0).abs() < 0.0001); // 1.0 * 2499 = 2499.0
    }

    #[test]
    #[should_panic]
    fn test_compute_orders_invalid_ring_index() {
        setup_test_rates();
        // 测试无效的环索引
        let _result = ArbitrageEngine::compute_orders(9999);
        // 这应该会 panic 或返回 None，取决于实现
        // 由于使用了 unsafe 访问，这可能会导致 panic
    }

    #[test]
    fn test_compute_orders_no_rates() {
        setup_test_rates();
        // 测试没有设置汇率的情况
        let result = ArbitrageEngine::compute_orders(0);
        assert_eq!(result, None);
    }

    #[test]
    fn test_compute_orders_profitable_scenario() {
        setup_test_rates();

        // 设置一个可能盈利的三角套利场景
        // 使用第一个环: ETHUSDT -> ETHUSDC -> SYRUPUSDC -> SYRUPUSDT
        ArbitrageEngine::update_rate(TradingPair::XETHUSDT, 2499.0, 2500.0);
        ArbitrageEngine::update_rate(TradingPair::XETHUSDC, 2498.0, 2499.0);
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDC, 0.99, 1.01);
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDT, 0.98, 1.02);

        let result = ArbitrageEngine::compute_orders(0);
        // 这个场景可能不会盈利，但至少应该能完成计算
        println!("Result: {:?}", result);
        println!("Order quantities: {:?}", unsafe { ORDER_QUANTITIES });
    }

    #[test]
    fn test_compute_orders_btc_scenario() {
        setup_test_rates();

        // 测试 BTC 相关的套利环
        // 寻找包含 BTCUSDT 的环
        if let Some(ring_index) = find_ring(
            TradingPair::XBTCUSDT,
            TradingPair::XETHBTC,
            TradingPair::XETHUSDT,
        ) {
            ArbitrageEngine::update_rate(TradingPair::XBTCUSDT, 50000.0, 50001.0);
            ArbitrageEngine::update_rate(TradingPair::XETHBTC, 0.05, 0.0501);
            ArbitrageEngine::update_rate(TradingPair::XETHUSDT, 2499.0, 2500.0);

            let result = ArbitrageEngine::compute_orders(ring_index);
            println!("BTC scenario result: {:?}", result);
            println!("Order quantities: {:?}", unsafe { ORDER_QUANTITIES });
        }
    }

    #[test]
    fn test_compute_orders_min_quantity_constraints() {
        setup_test_rates();

        // 测试最小数量约束
        // 设置非常小的初始数量，应该被最小数量约束拒绝
        ArbitrageEngine::update_rate(TradingPair::XETHUSDT, 2499.0, 2500.0);
        ArbitrageEngine::update_rate(TradingPair::XETHUSDC, 2498.0, 2499.0);
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDC, 0.99, 1.01);
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDT, 0.98, 1.02);

        let result = ArbitrageEngine::compute_orders(0);
        // 由于最小数量约束，这可能会失败
        println!("Min quantity test result: {:?}", result);
    }

    #[test]
    fn test_compute_orders_edge_cases() {
        setup_test_rates();

        // 测试边界情况
        // 1. 汇率为零
        ArbitrageEngine::update_rate(TradingPair::XETHUSDT, 0.0, 0.0);
        let result = ArbitrageEngine::compute_orders(0);
        assert_eq!(result, None);

        // 2. 极大的汇率
        setup_test_rates();
        ArbitrageEngine::update_rate(TradingPair::XETHUSDT, 1e10, 1e10);
        ArbitrageEngine::update_rate(TradingPair::XETHUSDC, 1e10, 1e10);
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDC, 1.0, 1.0);
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDT, 1.0, 1.0);
        let result = ArbitrageEngine::compute_orders(0);
        println!("Large rate test result: {:?}", result);
    }

    #[test]
    fn test_compute_orders_different_quote_currencies() {
        setup_test_rates();

        // 测试不同的计价货币初始数量
        // ETH 计价应该使用 0.008
        // BTC 计价应该使用 0.00002
        // 其他应该使用 20.0

        // 找一个 ETH 计价的环
        for (i, ring) in PREDEFINED_RINGS.iter().enumerate() {
            if ring.len() > 0 && ring[0].0.quote() == Currency::XETH {
                // 设置一些基本汇率
                for &(pair, _) in ring.iter() {
                    ArbitrageEngine::update_rate(pair, 1.0, 1.001);
                }
                let result = ArbitrageEngine::compute_orders(i);
                println!("ETH quote test (ring {}): {:?}", i, result);
                break;
            }
        }
    }

    #[test]
    fn test_compute_orders_try_count_iteration() {
        setup_test_rates();

        // 测试尝试计数迭代
        // 设置一个几乎可行但需要多次尝试的场景
        ArbitrageEngine::update_rate(TradingPair::XETHUSDT, 2499.0, 2500.0);
        ArbitrageEngine::update_rate(TradingPair::XETHUSDC, 2498.5, 2499.5);
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDC, 1.0, 1.001);
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDT, 0.999, 1.0);

        let result = ArbitrageEngine::compute_orders(0);
        println!("Try count iteration test result: {:?}", result);
        println!("Final order quantities: {:?}", unsafe { ORDER_QUANTITIES });
    }

    #[test]
    fn test_compute_orders_direction_logic() {
        setup_test_rates();

        // 测试不同方向的逻辑
        // 第一个环: ETHUSDT(Forward) -> ETHUSDC(Reverse) -> SYRUPUSDC(Forward) -> SYRUPUSDT(Reverse)

        // 设置汇率使得有明确的方向性
        ArbitrageEngine::update_rate(TradingPair::XETHUSDT, 2499.0, 2500.0); // Forward: 1/2500, Reverse: 2499
        ArbitrageEngine::update_rate(TradingPair::XETHUSDC, 2498.0, 2499.0); // Forward: 1/2499, Reverse: 2498
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDC, 0.99, 1.01); // Forward: 1/1.01, Reverse: 0.99
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDT, 0.98, 1.02); // Forward: 1/1.02, Reverse: 0.98

        let result = ArbitrageEngine::compute_orders(0);
        println!("Direction logic test result: {:?}", result);

        // 检查订单数量是否合理
        unsafe {
            use std::ptr::addr_of;
            let quantities_ptr = addr_of!(ORDER_QUANTITIES);
            println!("Order quantities: {:?}", *quantities_ptr);
            // 第一个订单应该是 Forward 方向的 ETHUSDT
            // 初始数量应该基于 ETH 的 quote currency (USDT)，所以应该是 20.0
        }
    }

    #[test]
    fn test_compute_orders_final_comparison_logic() {
        setup_test_rates();

        // 测试最终比较逻辑中的潜在 bug
        // 函数在最后比较 init_amount 和最终数量

        // 设置一个可能导致最终比较出错的场景
        ArbitrageEngine::update_rate(TradingPair::XETHUSDT, 2500.0, 2500.0); // 相同的 bid/ask
        ArbitrageEngine::update_rate(TradingPair::XETHUSDC, 2500.0, 2500.0);
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDC, 1.0, 1.0);
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDT, 1.0, 1.0);

        let result = ArbitrageEngine::compute_orders(0);
        println!("Final comparison test result: {:?}", result);

        // 在这种情况下，理论上应该没有套利机会（汇率相同）
        // 但函数可能仍然返回 Some，这可能是一个 bug
    }

    #[test]
    fn test_compute_orders_order_filter_constraints() {
        setup_test_rates();

        // 测试订单过滤器约束
        // 使用真实的订单过滤器数据来测试

        // 设置一些基本汇率
        ArbitrageEngine::update_rate(TradingPair::XETHUSDT, 2499.0, 2500.0);
        ArbitrageEngine::update_rate(TradingPair::XETHUSDC, 2498.0, 2499.0);
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDC, 0.99, 1.01);
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDT, 0.98, 1.02);

        let result = ArbitrageEngine::compute_orders(0);

        // 检查生成的订单数量是否符合过滤器约束
        unsafe {
            use std::ptr::addr_of;
            let ring = PREDEFINED_RINGS[0];
            let quantities_ptr = addr_of!(ORDER_QUANTITIES);
            for (i, &(pair, _)) in ring.iter().enumerate() {
                let order_filters = ORDER_FILTERS[pair as usize];
                let min_qty = order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX];
                let lot_size = order_filters[ORDER_FILTER_LOT_SIZE_INDEX];

                if (*quantities_ptr)[i] > 0.0 {
                    // 检查是否满足最小数量要求
                    assert!(
                        (*quantities_ptr)[i] >= min_qty,
                        "Order quantity {} for pair {:?} is below minimum {}",
                        (*quantities_ptr)[i],
                        pair,
                        min_qty
                    );

                    // 检查是否符合步长要求
                    let steps = ((*quantities_ptr)[i] / lot_size).round();
                    let expected = steps * lot_size;
                    assert!(
                        ((*quantities_ptr)[i] - expected).abs() < 1e-10,
                        "Order quantity {} for pair {:?} doesn't match lot size {}",
                        (*quantities_ptr)[i],
                        pair,
                        lot_size
                    );
                }
            }
        }

        println!("Order filter constraints test result: {:?}", result);
    }

    #[test]
    fn test_compute_orders_infinite_loop_protection() {
        setup_test_rates();

        // 测试无限循环保护
        // 设置一个可能导致无限循环的场景

        // 使用非常小的步长和最小数量，可能导致调整逻辑出现问题
        ArbitrageEngine::update_rate(TradingPair::XETHUSDT, 1e10, 1e10); // 极大的汇率
        ArbitrageEngine::update_rate(TradingPair::XETHUSDC, 1e10, 1e10);
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDC, 1e-10, 1e-10); // 极小的汇率
        ArbitrageEngine::update_rate(TradingPair::XSYRUPUSDT, 1e-10, 1e-10);

        // 这个测试应该在合理时间内完成，不应该无限循环
        let start = std::time::Instant::now();
        let result = ArbitrageEngine::compute_orders(0);
        let duration = start.elapsed();

        println!("Infinite loop protection test result: {:?}", result);
        println!("Test duration: {:?}", duration);

        // 确保测试在合理时间内完成（比如 1 秒）
        assert!(
            duration.as_secs() < 1,
            "Function took too long, possible infinite loop"
        );
    }
}
