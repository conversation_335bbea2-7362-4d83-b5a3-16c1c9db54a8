use memchr;
use std::str;

use crate::TradingPair;

#[derive(Debug)]
pub struct OrderId {
    pub order_create_time: u64, // it it not an timestamp, but a time represents by cpu circles
    pub ring_index: usize,
    pub edge_index: usize,
    pub is_testing: bool,
}

pub struct OrderCreated {
    pub order_id: OrderId,
    pub symbol: TradingPair,
}

impl OrderCreated {
    pub fn new(order_id: OrderId, symbol: TradingPair) -> Self {
        OrderCreated { order_id, symbol }
    }
}

#[derive(Debug)]
pub struct OrderUpdate {
    pub order_id: OrderId,
    pub fill_price: f64,
    pub fill_quantity: f64,
    pub symbol: TradingPair,
    pub status: String, // 执行状态：NEW, TRADE, FILLED 等
}

pub enum OrderResponse {
    OrderCreated(OrderCreated),
    OrderUpdate(OrderUpdate),
}

impl From<&str> for OrderId {
    fn from(s: &str) -> Self {
        let parts = s.split('-').collect::<Vec<&str>>();
        let order_create_time = parts[0].parse::<u64>().unwrap();
        let ring_index = parts[1].parse::<usize>().unwrap();
        let edge_index = parts[2].parse::<usize>().unwrap();
        let is_testing = parts[3] == "1";
        OrderId {
            order_create_time,
            ring_index,
            edge_index,
            is_testing,
        }
    }
}

impl OrderUpdate {
    pub fn new(order_id: OrderId, symbol: TradingPair, status: String) -> Self {
        OrderUpdate {
            order_id,
            fill_price: 0.0,
            fill_quantity: 0.0,
            symbol,
            status,
        }
    }
}

// #[perf_macro::measure]
pub fn parse_order_update(input: &[u8]) -> Option<OrderResponse> {
    let order_response_pattern = b"tOrderId\":\"";
    match memchr::memmem::find(input, order_response_pattern) {
        Some(start) => {
            let start = start + order_response_pattern.len();
            let end = memchr::memchr(b'"', &input[start..])?;
            let order_id = &input[start..start + end];
            let order_id: &str = unsafe { std::mem::transmute(order_id) };
            let order_id: OrderId = order_id.into();
            let start = memchr::memmem::find(input, b"\"symbol\":\"")? + 10;
            let end = memchr::memchr(b'"', &input[start..])?;
            let symbol = &input[start..start + end];
            let symbol: &str = unsafe { std::mem::transmute(symbol) };
            let symbol = TradingPair::from(symbol);
            let order_created = OrderCreated::new(order_id, symbol);
            Some(OrderResponse::OrderCreated(order_created))
        }
        None => {
            let order_update_pattern = b"executionReport";
            let _execution_report_pos = memchr::memmem::find(input, order_update_pattern)?;
            let status_pattern = b"x\":\"";
            let status_start = memchr::memmem::find(input, status_pattern)?;
            let start = status_start + status_pattern.len();
            let end = memchr::memchr(b'"', &input[start..])?;
            let exec_status = &input[start..start + end];
            let exec_status: &str = unsafe { std::mem::transmute(exec_status) };
            // Handle any execution status (NEW, TRADE, FILLED, REJECTED, EXPIRED, CANCELED, etc.)
            if exec_status == "TRADE"
                || exec_status == "NEW"
                || exec_status == "FILLED"
                || exec_status == "REJECTED"
                || exec_status == "EXPIRED"
                || exec_status == "CANCELED"
            {
                let order_id_pattern = b"\"c\":\"";
                let start = memchr::memmem::find(input, order_id_pattern)? + order_id_pattern.len();
                let end = memchr::memchr(b'"', &input[start..])?;
                let order_id = &input[start..start + end];
                let order_id: &str = unsafe { std::mem::transmute(order_id) };
                let order_id: OrderId = order_id.into();

                let symbol_pattern = b"\"s\":\"";
                let symbol = memchr::memmem::find(input, symbol_pattern)? + symbol_pattern.len();
                let end = memchr::memchr(b'"', &input[symbol..])?;
                let symbol = &input[symbol..symbol + end];
                let symbol: &str = unsafe { std::mem::transmute(symbol) };
                let symbol = TradingPair::from(symbol);

                let mut order_update = OrderUpdate::new(order_id, symbol, exec_status.to_string());

                // Only parse fill price and quantity for TRADE status
                if exec_status == "TRADE" {
                    // Parse fill price (L field)
                    let price_pattern = b"\"L\":\"";
                    if let Some(start) = memchr::memmem::find(input, price_pattern) {
                        let start = start + price_pattern.len();
                        if let Some(end) = memchr::memchr(b'"', &input[start..]) {
                            let fill_price = &input[start..start + end];
                            let fill_price: &str = unsafe { std::mem::transmute(fill_price) };
                            if let Ok(price) = fill_price.parse::<f64>() {
                                order_update.fill_price = price;
                            }
                        }
                    }

                    // Parse fill quantity (l field - lowercase L)
                    let qty_pattern = b"\"l\":\"";
                    if let Some(start) = memchr::memmem::find(input, qty_pattern) {
                        let start = start + qty_pattern.len();
                        if let Some(end) = memchr::memchr(b'"', &input[start..]) {
                            let fill_qty = &input[start..start + end];
                            let fill_qty: &str = unsafe { std::mem::transmute(fill_qty) };
                            if let Ok(qty) = fill_qty.parse::<f64>() {
                                order_update.fill_quantity = qty;
                            }
                        }
                    }
                }

                Some(OrderResponse::OrderUpdate(order_update))
            } else {
                None
            }
        }
    }
}

#[cfg(test)]
mod tests {

    use super::*;
    #[test]
    fn test_parse_order_update() {
        let data = r#"
{
  "id": "341935215578177",
  "status": 200,
  "result": {
    "symbol":"BTCUSDT",
    "orderId": 43735702641,
    "orderListId": -1,
    "clientOrderId":"341935215578177-0-0",
    "transactTime": 1748250466926,
    "price": "109624.00000000",
    "origQty": "0.00011000",
    "executedQty": "0.00000000",
    "origQuoteOrderQty": "0.00000000",
    "cummulativeQuoteQty": "0.00000000",
    "status": "EXPIRED",
    "timeInForce": "IOC",
    "type": "LIMIT",
    "side": "BUY",
    "workingTime": 1748250466926,
    "fills": [],
    "selfTradePreventionMode": "EXPIRE_MAKER"
  },
  "rateLimits": [
    {
      "rateLimitType": "ORDERS",
      "interval": "SECOND",
      "intervalNum": 10,
      "limit": 100,
      "count": 90
    },
    {
      "rateLimitType": "ORDERS",
      "interval": "DAY",
      "intervalNum": 1,
      "limit": 200000,
      "count": 40240
    },
    {
      "rateLimitType": "REQUEST_WEIGHT",
      "interval": "MINUTE",
      "intervalNum": 1,
      "limit": 6000,
      "count": 141
    }
  ]
}

        "#;
        let r = parse_order_update(data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderCreated(order_created) = r.unwrap() else {
            panic!("Expected OrderCreated");
        };
        assert_eq!(order_created.order_id.order_create_time, 341935215578177);
        assert_eq!(order_created.order_id.ring_index, 0);
        assert_eq!(order_created.order_id.edge_index, 0);
        assert_eq!(order_created.symbol, TradingPair::XBTCUSDT);
    }

    #[test]
    fn test_parse_order_update_optimized() {
        let data = r#"
{
  "event": {
    "e": "executionReport",
    "E": 1748250466926,
    "s":"BTCUSDT",
    "c":"341935215578177-0-0",
    "S": "BUY",
    "o": "LIMIT",
    "f": "IOC",
    "q": "0.00011000",
    "p":"109624.00000000",
    "P": "0.00000000",
    "F": "0.00000000",
    "g": -1,
    "C":"",
    "x":"TRADE",
    "X":"TRADE",
    "r": "NONE",
    "i": 43735702642,
    "l": "0.00000000",
    "z": "0.00000000",
    "L":"0.00000000",
    "n": "0",
    "N": null,
    "T": 1748250466926,
    "t": -1,
    "I": 93070863163,
    "w": false,
    "m": false,
    "M": false,
    "O": 1748250466926,
    "Z": "0.00000000",
    "Y": "0.00000000",
    "Q": "0.00000000",
    "W": 1748250466926,
    "V": "EXPIRE_MAKER"
  }
}

        "#;
        let r = parse_order_update(data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.order_id.order_create_time, 341935215578177);
        assert_eq!(order_update.order_id.ring_index, 0);
        assert_eq!(order_update.order_id.edge_index, 0);
        assert_eq!(order_update.symbol, TradingPair::XBTCUSDT);
    }

    #[test]
    fn test_parse_order_update_new_status() {
        let data = r#"
{
  "event": {
    "e": "executionReport",
    "E": 1748250466926,
    "s":"BTCUSDT",
    "c":"341935215578177-0-0",
    "S": "BUY",
    "o": "LIMIT",
    "f": "IOC",
    "q": "0.00011000",
    "p":"109624.00000000",
    "x":"NEW",
    "X":"NEW",
    "i": 43735702642,
    "O": 1748250466926
  }
}
        "#;
        let r = parse_order_update(data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.order_id.order_create_time, 341935215578177);
        assert_eq!(order_update.order_id.ring_index, 0);
        assert_eq!(order_update.order_id.edge_index, 0);
        assert_eq!(order_update.symbol, TradingPair::XBTCUSDT);
        assert_eq!(order_update.fill_price, 0.0); // No fill price for NEW status
        assert_eq!(order_update.fill_quantity, 0.0); // No fill quantity for NEW status
    }

    #[test]
    fn test_parse_order_update_trade_status() {
        let data = r#"
{
  "event": {
    "e": "executionReport",
    "E": 1748250466926,
    "s":"BTCUSDT",
    "c":"341935215578177-0-0",
    "S": "BUY",
    "o": "LIMIT",
    "f": "IOC",
    "q": "0.00011000",
    "p":"109624.00000000",
    "x":"TRADE",
    "X":"FILLED",
    "i": 43735702642,
    "l":"0.00011000",
    "L":"109624.00000000",
    "O": 1748250466926
  }
}
        "#;
        let r = parse_order_update(data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.order_id.order_create_time, 341935215578177);
        assert_eq!(order_update.order_id.ring_index, 0);
        assert_eq!(order_update.order_id.edge_index, 0);
        assert_eq!(order_update.symbol, TradingPair::XBTCUSDT);
        assert_eq!(order_update.fill_price, 109624.0); // Fill price for TRADE status
        assert_eq!(order_update.fill_quantity, 0.00011); // Fill quantity for TRADE status
    }

    #[test]
    fn test_order_update_status_field() {
        let new_data = r#"
{
  "event": {
    "e": "executionReport",
    "E": 1748250466926,
    "s":"BTCUSDT",
    "c":"341935215578177-0-0",
    "x":"NEW",
    "X":"NEW",
    "i": 43735702642,
    "O": 1748250466926
  }
}
        "#;
        let r = parse_order_update(new_data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.status, "NEW");

        let trade_data = r#"
{
  "event": {
    "e": "executionReport",
    "E": 1748250466926,
    "s":"BTCUSDT",
    "c":"341935215578177-0-0",
    "x":"TRADE",
    "X":"FILLED",
    "i": 43735702642,
    "l":"0.00011000",
    "L":"109624.00000000",
    "O": 1748250466926
  }
}
        "#;
        let r = parse_order_update(trade_data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.status, "TRADE");
    }

    #[test]
    fn test_parse_order_update_error_statuses() {
        // Test REJECTED status
        let rejected_data = r#"
{
  "event": {
    "e": "executionReport",
    "E": 1748250466926,
    "s":"BTCUSDT",
    "c":"341935215578177-0-0",
    "S": "BUY",
    "o": "LIMIT",
    "f": "IOC",
    "q": "0.00011000",
    "p":"109624.00000000",
    "x":"REJECTED",
    "X":"REJECTED",
    "i": 43735702642,
    "O": 1748250466926
  }
}
        "#;

        let r = parse_order_update(rejected_data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.status, "REJECTED");
        assert_eq!(order_update.order_id.order_create_time, 341935215578177);

        // Test EXPIRED status
        let expired_data = r#"
{
  "event": {
    "e": "executionReport",
    "E": 1748250466926,
    "s":"BTCUSDT",
    "c":"341935215578177-1-2",
    "S": "BUY",
    "o": "LIMIT",
    "f": "IOC",
    "q": "0.00011000",
    "p":"109624.00000000",
    "x":"EXPIRED",
    "X":"EXPIRED",
    "i": 43735702642,
    "O": 1748250466926
  }
}
        "#;

        let r = parse_order_update(expired_data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.status, "EXPIRED");
        assert_eq!(order_update.order_id.ring_index, 1);
        assert_eq!(order_update.order_id.edge_index, 2);

        // Test CANCELED status
        let canceled_data = r#"
{
  "event": {
    "e": "executionReport",
    "E": 1748250466926,
    "s":"BTCUSDT",
    "c":"341935215578177-2-1",
    "S": "BUY",
    "o": "LIMIT",
    "f": "IOC",
    "q": "0.00011000",
    "p":"109624.00000000",
    "x":"CANCELED",
    "X":"CANCELED",
    "i": 43735702642,
    "O": 1748250466926
  }
}
        "#;

        let r = parse_order_update(canceled_data.as_bytes());
        assert!(r.is_some());
        let OrderResponse::OrderUpdate(order_update) = r.unwrap() else {
            panic!("Expected OrderUpdate");
        };
        assert_eq!(order_update.status, "CANCELED");
        assert_eq!(order_update.order_id.ring_index, 2);
        assert_eq!(order_update.order_id.edge_index, 1);
    }
}
